import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import '../../../../shared/providers/auth_provider.dart';
import '../../data/models/meal_plan_request.dart';
import '../../data/services/meal_plan_service.dart';

part 'meal_plan_generation_provider.freezed.dart';
part 'meal_plan_generation_provider.g.dart';

@freezed
class MealPlanGenerationState with _$MealPlanGenerationState {
  const factory MealPlanGenerationState({
    @Default(false) bool isLoading,
    @Default(false) bool isGenerating,
    MealPlanResponse? generatedPlan,
    String? error,
    @Default(MealPlanPreferences()) MealPlanPreferences preferences,
    @Default(3) int duration,
    @Default(false) bool showAdvancedSettings,
  }) = _MealPlanGenerationState;
}

@riverpod
class MealPlanGenerationNotifier extends _$MealPlanGenerationNotifier {
  @override
  MealPlanGenerationState build() {
    return const MealPlanGenerationState();
  }

  /// Update diet type
  void updateDietType(DietType dietType) {
    state = state.copyWith(
      preferences: state.preferences.copyWith(dietType: dietType),
    );
  }

  /// Update calorie goal
  void updateCalorieGoal(int calories) {
    state = state.copyWith(
      preferences: state.preferences.copyWith(calorieGoal: calories),
    );
  }

  /// Update protein goal
  void updateProteinGoal(int protein) {
    state = state.copyWith(
      preferences: state.preferences.copyWith(proteinGoal: protein),
    );
  }

  /// Update carbs goal
  void updateCarbsGoal(int carbs) {
    state = state.copyWith(
      preferences: state.preferences.copyWith(carbsGoal: carbs),
    );
  }

  /// Update fat goal
  void updateFatGoal(int fat) {
    state = state.copyWith(
      preferences: state.preferences.copyWith(fatGoal: fat),
    );
  }

  /// Update meals per day
  void updateMealsPerDay(int meals) {
    state = state.copyWith(
      preferences: state.preferences.copyWith(mealsPerDay: meals),
    );
  }

  /// Update duration
  void updateDuration(int duration) {
    state = state.copyWith(duration: duration);
  }

  /// Toggle advanced settings visibility
  void toggleAdvancedSettings() {
    state = state.copyWith(showAdvancedSettings: !state.showAdvancedSettings);
  }

  /// Add dietary restriction
  void addDietaryRestriction(String restriction) {
    final restrictions = List<String>.from(state.preferences.dietaryRestrictions);
    if (!restrictions.contains(restriction)) {
      restrictions.add(restriction);
      state = state.copyWith(
        preferences: state.preferences.copyWith(dietaryRestrictions: restrictions),
      );
    }
  }

  /// Remove dietary restriction
  void removeDietaryRestriction(String restriction) {
    final restrictions = List<String>.from(state.preferences.dietaryRestrictions);
    restrictions.remove(restriction);
    state = state.copyWith(
      preferences: state.preferences.copyWith(dietaryRestrictions: restrictions),
    );
  }

  /// Add allergy
  void addAllergy(String allergy) {
    final allergies = List<String>.from(state.preferences.allergies);
    if (!allergies.contains(allergy)) {
      allergies.add(allergy);
      state = state.copyWith(
        preferences: state.preferences.copyWith(allergies: allergies),
      );
    }
  }

  /// Remove allergy
  void removeAllergy(String allergy) {
    final allergies = List<String>.from(state.preferences.allergies);
    allergies.remove(allergy);
    state = state.copyWith(
      preferences: state.preferences.copyWith(allergies: allergies),
    );
  }

  /// Add cuisine preference
  void addCuisinePreference(String cuisine) {
    final cuisines = List<String>.from(state.preferences.cuisinePreferences);
    if (!cuisines.contains(cuisine)) {
      cuisines.add(cuisine);
      state = state.copyWith(
        preferences: state.preferences.copyWith(cuisinePreferences: cuisines),
      );
    }
  }

  /// Remove cuisine preference
  void removeCuisinePreference(String cuisine) {
    final cuisines = List<String>.from(state.preferences.cuisinePreferences);
    cuisines.remove(cuisine);
    state = state.copyWith(
      preferences: state.preferences.copyWith(cuisinePreferences: cuisines),
    );
  }

  /// Generate meal plan
  /// Preferences are automatically retrieved from user profile in Firestore
  Future<void> generateMealPlan() async {
    final userProfile = ref.read(currentUserProfileProvider);
    if (userProfile == null) {
      state = state.copyWith(error: 'User not authenticated');
      return;
    }

    state = state.copyWith(
      isGenerating: true,
      error: null,
      generatedPlan: null,
    );

    try {
      final mealPlanService = ref.read(mealPlanServiceProvider);

      final response = await mealPlanService.generateMealPlan(
        duration: state.duration,
      );

      state = state.copyWith(
        isGenerating: false,
        generatedPlan: response,
      );
    } catch (e) {
      state = state.copyWith(
        isGenerating: false,
        error: e.toString(),
      );
    }
  }

  /// Reset state
  void reset() {
    state = const MealPlanGenerationState();
  }

  /// Load user preferences from profile
  void loadUserPreferences() {
    final userProfile = ref.read(currentUserProfileProvider);
    if (userProfile == null) return;

    state = state.copyWith(
      preferences: state.preferences.copyWith(
        calorieGoal: userProfile.dailyCalorieGoal ?? 2000,
        proteinGoal: userProfile.proteinGoal?.toInt() ?? 0,
        carbsGoal: userProfile.carbsGoal?.toInt() ?? 0,
        fatGoal: userProfile.fatGoal?.toInt() ?? 0,
        mealsPerDay: userProfile.mealsPerDay,
        dietaryRestrictions: userProfile.dietaryRestrictions,
        allergies: userProfile.allergies,
      ),
    );
  }
}
