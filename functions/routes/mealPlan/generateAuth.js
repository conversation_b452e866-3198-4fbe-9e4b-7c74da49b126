const {authenticateUser} = require('../../middleware/auth');
const {authenticatedMealPlanRequestSchema} = require('../../utils/validators');
const {
  generateMealPlan,
  saveMealPlan,
} = require('../../services/mealPlanService');
const {getUserProfile} = require('../../services/userService');

/**
 * POST /meal-plans/generate
 * Authenticated version of meal plan generation
 * Requires authentication - gets preferences from user profile
 */
async function generateAuthenticatedMealPlan(req, res) {
  try {
    // Validate request (only duration is required)
    const {error, value} = authenticatedMealPlanRequestSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        error: error.details[0].message,
      });
    }

    const {duration} = value;
    const userId = req.user.uid; // From auth middleware

    // Get user profile from Firestore
    const userProfile = await getUserProfile(userId);
    if (!userProfile) {
      return res.status(404).json({
        error: 'User profile not found. Please complete onboarding first.',
      });
    }

    // Extract preferences from user profile
    const preferences = {
      dietaryRestrictions: userProfile.dietaryRestrictions || [],
      allergies: userProfile.allergies || [],
      cuisinePreferences: userProfile.favoriteCuisines || [],
      mealsPerDay: userProfile.mealsPerDay || 3,
      calorieGoal: userProfile.dailyCalorieGoal || 2000,
      proteinGoal: Math.round(userProfile.proteinGoal || 0),
      carbsGoal: Math.round(userProfile.carbsGoal || 0),
      fatGoal: Math.round(userProfile.fatGoal || 0),
      dietType: 'normal', // Default diet type, can be enhanced later
    };

    // Generate meal plan
    const mealPlan = await generateMealPlan(preferences, duration, userId);

    // Save to Firestore
    await saveMealPlan(userId, mealPlan, preferences, duration);

    res.json({
      success: true,
      mealPlan,
      message: 'Meal plan generated successfully',
    });
  } catch (error) {
    console.error('Error generating meal plan:', error);
    res.status(500).json({
      error: 'Failed to generate meal plan',
      details: error instanceof Error ? error.message : 'Unknown error',
    });
  }
}

module.exports = {
  method: 'post',
  path: '/meal-plans/generate',
  middleware: [authenticateUser],
  handler: generateAuthenticatedMealPlan,
};
